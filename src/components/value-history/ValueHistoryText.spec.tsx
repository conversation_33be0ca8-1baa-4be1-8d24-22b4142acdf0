import { numberOneWithInput, percentageOneWithInput, numberTwoWithCurrency } from '@fixtures/utr/utr-base-fixtures';
import { utrvNumericValueListOne } from '@fixtures/utr/utr-numericValueList-fixtures';
import { utrvTextValueListOne } from '@fixtures/utr/utr-textValueList-fixtures';
import { utrSingleRowTableOne, utrSingleRowTableOneWithCurrency, utrTableOne, utrTableOneUtrv } from '@fixtures/utr/utr-table-fixtures';
import { utrvValueListOne } from '@fixtures/utr/utr-ValueList-fixtures';
import { partialFieldFixtures } from '@fixtures/assuranceUtrv-fixture';
import UniversalTracker from '@models/UniversalTracker';
import { ValueHistoryText } from './ValueHistoryText';
import { generateValueHistory } from '@fixtures/questions-fixture';
import { UtrvAssuranceStatus } from '@g17eco/types/universalTrackerValue';
import { UtrvStatus } from '@constants/status';
import { render, screen } from '@testing-library/react';
import { reduxFixtureStore } from '@fixtures/redux-store';
import { Provider } from 'react-redux';
import { UnitConfig } from '@models/surveyData';
import { renderWithProviders } from '@fixtures/utils';
import userEvent from '@testing-library/user-event';

const twoWhitespaceRegex = / {2}/g;

const formatText = (str: string | null) => (str ? str.replace(twoWhitespaceRegex, ' ').trim() : str);

// Utr fixtures
const numericUtr = new UniversalTracker(numberOneWithInput.universalTracker);
const percentageUtr = new UniversalTracker(percentageOneWithInput.universalTracker);
const valueListUtr = new UniversalTracker(utrvValueListOne.universalTracker);
const numericValueListUtr = new UniversalTracker(utrvNumericValueListOne.universalTracker);
const textValueListUtr = new UniversalTracker(utrvTextValueListOne.universalTracker);
const singleRowTableUtr = new UniversalTracker(utrSingleRowTableOne);
const multiRowTableUtr = new UniversalTracker(utrTableOne);
const currencyUtr = new UniversalTracker(numberTwoWithCurrency.universalTracker);
const tableWithCurrencyUtr = new UniversalTracker(utrSingleRowTableOneWithCurrency);

describe('ValueHistoryText', () => {
  describe('partialAssuranceProvenance fn', () => {
    it('should return null when history assurance status is not partial', () => {
      const result = ValueHistoryText.partialAssuranceProvenance({
        history: generateValueHistory({
          assuranceStatus: UtrvAssuranceStatus.Created,
          assuranceFields: partialFieldFixtures.numericValueList,
          valueData: utrvNumericValueListOne.valueData,
        }),
        universalTracker: numericValueListUtr,
      });
      expect(result).toBeNull();
    });

    it('should return null when utr is number/percentage/text', () => {
      const result = ValueHistoryText.partialAssuranceProvenance({
        history: generateValueHistory({ assuranceStatus: UtrvAssuranceStatus.Partial }),
        universalTracker: numericUtr,
      });
      expect(result).toBeNull();
    });

    it('should return null when utr is valueList', () => {
      const result = ValueHistoryText.partialAssuranceProvenance({
        history: generateValueHistory({ assuranceStatus: UtrvAssuranceStatus.Partial }),
        universalTracker: valueListUtr,
      });
      expect(result).toBeNull();
    });

    it('should render list items for assured values when utr is numericValueList', () => {
      // numericValueList
      const numericValueListResult = ValueHistoryText.partialAssuranceProvenance({
        history: generateValueHistory({
          assuranceStatus: UtrvAssuranceStatus.Partial,
          assuranceFields: partialFieldFixtures.numericValueList,
          valueData: utrvNumericValueListOne.valueData,
        }),
        universalTracker: numericValueListUtr,
      });
      expect(numericValueListResult?.type).toEqual('ol');
      expect(numericValueListResult?.props?.className).toContain('partial-assurance-list');
      expect(numericValueListResult?.props?.children).toHaveLength(2);
    });

    it('should render list items for assured values when utr is textValueList', () => {
      const textValueListResult = ValueHistoryText.partialAssuranceProvenance({
        history: generateValueHistory({
          assuranceStatus: UtrvAssuranceStatus.Partial,
          assuranceFields: partialFieldFixtures.textValueList,
          valueData: utrvTextValueListOne.valueData,
        }),
        universalTracker: textValueListUtr,
      });
      expect(textValueListResult?.type).toEqual('ol');
      expect(textValueListResult?.props?.className).toContain('partial-assurance-list');
      expect(textValueListResult?.props?.children).toHaveLength(1);
    });

    it('should render list items for assured values when utr is singleRowTable', () => {
      const result = ValueHistoryText.partialAssuranceProvenance({
        history: generateValueHistory({
          assuranceStatus: UtrvAssuranceStatus.Partial,
          assuranceFields: partialFieldFixtures.singleRowTable,
          valueData: utrTableOneUtrv.valueData,
        }),
        universalTracker: singleRowTableUtr,
      });
      expect(result?.type).toEqual('ol');
      expect(result?.props?.className).toContain('partial-assurance-list');
      expect(result?.props?.children).toHaveLength(6);
    });

    it('should render a table for assured values when utr is multiRowTable', () => {
      const result = ValueHistoryText.partialAssuranceProvenance({
        history: generateValueHistory({
          assuranceStatus: UtrvAssuranceStatus.Partial,
          assuranceFields: partialFieldFixtures.multiRowTable,
          valueData: utrTableOneUtrv.valueData,
        }),
        universalTracker: multiRowTableUtr,
      });
      expect(typeof result?.type).toEqual('function');
      expect(typeof result?.props?.disabled).toBeTruthy();
    });
  });

  describe('getHistoryText', () => {
    const className = 'updated';
    const username = 'Full Name';
    describe('Update', () => {
      const statusText = 'Updated';
      it('number question - no unit, no number scale', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          value: 10,
          valueData: { input: { value: 10 } },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );
        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('10');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('number question - has unit - unit unchanged', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          unit: 'mJ',
          value: 1,
          valueData: {
            input: {
              unit: 'mJ',
              value: 1,
            },
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );

        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('1 Megajoule');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('number question - has unit - unit changed', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          unit: 'mJ',
          value: 1,
          valueData: {
            input: {
              unit: 'J',
              value: 1000000,
            },
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );

        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('1,000,000 Joules - Defaults to 1.000 Megajoules');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('number question - has unit & overridden number scale - unit & number scale unchanged', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          unit: 'mJ',
          numberScale: 'single',
          value: 1,
          valueData: {
            input: {
              unit: 'mJ',
              value: 1,
              numberScale: 'single',
            },
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );

        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('1 Megajoule');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('number question - has unit & overridden number scale - number scale changed', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          unit: 'mJ',
          numberScale: 'single',
          value: 1,
          valueData: {
            input: {
              unit: 'mJ',
              value: 1,
              numberScale: 'thousands',
            },
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );

        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('1 Thousand Megajoule - Defaults to 1,000.000 Megajoule');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('number question - has unit & overridden number scale - both unit & number scale changed', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          unit: 'mJ',
          numberScale: 'single',
          value: 1,
          valueData: {
            input: {
              unit: 'kJ',
              value: 1,
              numberScale: 'thousands',
            },
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );

        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('1 Thousand Kilojoule - Defaults to 1.000 Megajoule');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('percentage question - no overridden number scale - number scale unchanged', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          value: 10,
          valueData: { input: { value: 10 } },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: percentageUtr,
          }),
        );
        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('10 %');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('percentage question - has overridden number scale', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          value: 10,
          numberScale: 'single',
          valueData: { input: { value: 10, numberScale: 'single' } },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: percentageUtr,
          }),
        );
        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('10 %');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('percentage question - has overridden number scale - number scale changed', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          value: 10,
          numberScale: 'single',
          valueData: { input: { value: 10, numberScale: 'thousands' } },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: percentageUtr,
          }),
        );
        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('10 Thousands % - Defaults to 10,000.000 %');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });
    });

    describe('Verified', () => {
      const statusText = 'Verified';
      it('should render a collapsible component', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Verified,
          value: 10,
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Verified by Full Name');
      });
    });

    describe('Partially Assured', () => {
      const statusText = 'Verified';
      it('should render a collapsible component', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Verified,
          assuranceStatus: UtrvAssuranceStatus.Partial,
          value: 10,
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Partially assured by Full Name');
      });
    });

    describe('Text Value List', () => {
      const statusText = 'Updated';
      it('should render text value list items', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          valueData: {
            data: {
              option1: 'Value 1',
              option2: 'Value 2',
            },
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: textValueListUtr,
          }),
        );
        const historyText = screen.getByTestId('history-text');
        expect(historyText.querySelectorAll('li')).toHaveLength(2);
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });
    });

    describe('Table', () => {
      const statusText = 'Updated';
      const store = reduxFixtureStore();
      it('should render table data', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          valueData: {
            table: [
              [
                { code: 'col1', value: 'Value 1' },
                { code: 'col2', value: 'Value 2' },
              ],
            ],
          },
        });

        render(
          <Provider store={store}>
            {ValueHistoryText.getHistoryText({
              className,
              status: statusText,
              history,
              username,
              universalTracker: singleRowTableUtr,
            })}
          </Provider>,
        );
        expect(screen.getByTestId('action-username')).toBeInTheDocument();
      });
    });

    describe('UnitConfig Parameter Tests', () => {
      const statusText = 'Updated';
      const store = reduxFixtureStore();
      const defaultUnitConfig: UnitConfig = {
        area: 'km2',
        length: 'km',
        time: 'h',
        mass: 'mt',
        volume: 'm3',
        energy: 'MWh',
        currency: 'USD',
        co2Emissions: 'tons/CO2e',
        numberScale: 'millions',
        partsPer: 'ppm',
      };

      describe('CollapsibleValueHistoryText path', () => {
        it('should render currency values with SGD configuration', () => {
          const surveyUnitConfig: UnitConfig = {
            ...defaultUnitConfig,
            currency: 'SGD',
          };

          const history = generateValueHistory({
            action: UtrvStatus.Updated,
            value: 2000,
            valueData: {
              input: { value: 2000, unit: 'SGD' }
            }
          });

          renderWithProviders(
            ValueHistoryText.getHistoryText({
              className,
              status: statusText,
              history,
              username,
              universalTracker: currencyUtr,
              unitConfig: surveyUnitConfig,
            }),
            { store }
          );

          expect(screen.getByTestId('action-username')).toBeInTheDocument();
          expect(screen.getByText('Submitted')).toBeInTheDocument();
          expect(screen.getByText('Full Name')).toBeInTheDocument();

          // Verify SGD currency is rendered correctly in the history text
          expect(screen.getByTestId('history-text')).toBeInTheDocument();
          expect(screen.getByText('SGD')).toBeInTheDocument();
          expect(screen.getByText('2,000')).toBeInTheDocument();
        });

        it('should handle undefined unitConfig and still render currency values', () => {
          const history = generateValueHistory({
            action: UtrvStatus.Updated,
            value: 1000,
            valueData: {
              input: { value: 1000, unit: 'GBP' }
            }
          });

          renderWithProviders(
            ValueHistoryText.getHistoryText({
              className,
              status: statusText,
              history,
              username,
              universalTracker: currencyUtr,
              unitConfig: undefined,
            }),
            { store }
          );

          expect(screen.getByTestId('action-username')).toBeInTheDocument();
          expect(screen.getByText('Submitted')).toBeInTheDocument();
          expect(screen.getByText('Full Name')).toBeInTheDocument();

          // Verify GBP currency is rendered correctly even without unitConfig
          expect(screen.getByTestId('history-text')).toBeInTheDocument();
          expect(screen.getByText('GBP')).toBeInTheDocument();
          expect(screen.getByText('1,000')).toBeInTheDocument();
        });
      });

      describe('Table view with unitConfig', () => {
        it('should render table with EUR currency configuration', () => {
          const surveyUnitConfig: UnitConfig = {
            ...defaultUnitConfig,
            currency: 'EUR',
          };

          const history = generateValueHistory({
            action: UtrvStatus.Updated,
            valueData: {
              table: [
                [
                  { code: 'currency_col', value: 2000 },
                  { code: 'number_col2', value: 750 },
                ],
              ],
            },
          });

          renderWithProviders(
            ValueHistoryText.getHistoryText({
              className,
              status: statusText,
              history,
              username,
              universalTracker: tableWithCurrencyUtr,
              unitConfig: surveyUnitConfig,
            }),
            { store }
          );

          expect(screen.getByTestId('action-username')).toBeInTheDocument();
          expect(screen.getByText('Submitted')).toBeInTheDocument();
          expect(screen.getByText('Full Name')).toBeInTheDocument();

          // Verify table is rendered with EUR unitConfig
          expect(screen.getByRole('table')).toBeInTheDocument();
          expect(screen.getByText('Currency Col 1')).toBeInTheDocument();
          expect(screen.getByText(/EUR/)).toBeInTheDocument();
          expect(screen.getByText('Number Col 2')).toBeInTheDocument();
          expect(screen.getByText('750')).toBeInTheDocument();
        });

        it('should render table values without currency if unitConfig is not provided', () => {
          const currencyValue = 1500;
          const history = generateValueHistory({
            action: UtrvStatus.Updated,
            valueData: {
              table: [
                [
                  { code: 'currency_col', value: currencyValue },
                  { code: 'number_col2', value: 300 },
                ],
              ],
            },
          });

          renderWithProviders(
            ValueHistoryText.getHistoryText({
              className,
              status: statusText,
              history,
              username,
              universalTracker: tableWithCurrencyUtr,
              unitConfig: undefined,
            }),
            { store }
          );

          expect(screen.getByTestId('action-username')).toBeInTheDocument();
          expect(screen.getByText('Submitted')).toBeInTheDocument();
          expect(screen.getByText('Full Name')).toBeInTheDocument();

          // Verify table is still rendered without unitConfig
          expect(screen.getByRole('table')).toBeInTheDocument();
          expect(screen.getByText('Currency Col 1')).toBeInTheDocument();
          expect(screen.getByText(currencyValue.toString())).toBeInTheDocument();
          expect(screen.getByText('Number Col 2')).toBeInTheDocument();
          expect(screen.getByText('300')).toBeInTheDocument();
        });
      });

      describe('Currency-specific edge cases', () => {
        it('should render partial assurance status with unitConfig', async () => {
          const history = generateValueHistory({
            action: UtrvStatus.Updated,
            assuranceStatus: UtrvAssuranceStatus.Partial,
            value: 1000,
            valueData: {
              input: { value: 1000, unit: 'GBP' }
            }
          });

          renderWithProviders(
            ValueHistoryText.getHistoryText({
              className,
              status: statusText,
              history,
              username,
              universalTracker: currencyUtr,
              unitConfig: defaultUnitConfig,
            }),
            { store }
          );

          // For partial assurance, should render the partial assurance toggle
          const collapseButton = screen.getByTestId('action-username');
          expect(collapseButton).toBeInTheDocument();
          expect(screen.getByText(/Partially assured/)).toBeInTheDocument();
          expect(screen.getByText(/Full Name/)).toBeInTheDocument();

          await userEvent.click(collapseButton);
          expect(await screen.findByText('GBP')).toBeInTheDocument();
        });

        it('should render Created status with currency values', () => {
          const history = generateValueHistory({
            action: UtrvStatus.Created,
            value: 1000,
            valueData: {
              input: { value: 1000, unit: 'GBP' }
            }
          });

          renderWithProviders(
            ValueHistoryText.getHistoryText({
              className,
              status: 'Created',
              history,
              username,
              universalTracker: currencyUtr,
              unitConfig: defaultUnitConfig,
            }),
            { store }
          );

          // Created status should render simple action username
          expect(screen.getByTestId('action-username')).toBeInTheDocument();
          expect(screen.getByText(/Created/)).toBeInTheDocument();
          expect(screen.getByText(/Full Name/)).toBeInTheDocument();
        });

        it('should render Verified status with currency values', () => {
          const history = generateValueHistory({
            action: UtrvStatus.Verified,
            value: 1000,
            valueData: {
              input: { value: 1000, unit: 'GBP' }
            }
          });

          renderWithProviders(
            ValueHistoryText.getHistoryText({
              className,
              status: 'Verified',
              history,
              username,
              universalTracker: currencyUtr,
              unitConfig: defaultUnitConfig,
            }),
            { store }
          );

          // Verified status should render simple action username
          expect(screen.getByTestId('action-username')).toBeInTheDocument();
          expect(screen.getByText('Verified')).toBeInTheDocument();
          expect(screen.getByText('Full Name')).toBeInTheDocument();
        });

        it('should render different currency values correctly in DOM', () => {
          const sgdUnitConfig: UnitConfig = {
            ...defaultUnitConfig,
            currency: 'SGD',
          };

          const history = generateValueHistory({
            action: UtrvStatus.Updated,
            value: 2000,
            valueData: {
              input: { value: 2000, unit: 'SGD' }
            }
          });

          renderWithProviders(
            ValueHistoryText.getHistoryText({
              className,
              status: statusText,
              history,
              username,
              universalTracker: currencyUtr,
              unitConfig: sgdUnitConfig,
            }),
            { store }
          );

          expect(screen.getByTestId('action-username')).toBeInTheDocument();
          expect(screen.getByText('Submitted')).toBeInTheDocument();
          expect(screen.getByText('Full Name')).toBeInTheDocument();

          // Verify SGD currency is rendered correctly
          expect(screen.getByTestId('history-text')).toBeInTheDocument();
          expect(screen.getByText('SGD')).toBeInTheDocument();
          expect(screen.getByText('2,000')).toBeInTheDocument();
        });

        it('should handle empty unitConfig and render currency values in DOM', () => {
          const emptyUnitConfig = {} as UnitConfig;

          const history = generateValueHistory({
            action: UtrvStatus.Updated,
            value: 1000,
            valueData: {
              input: { value: 1000, unit: 'GBP' }
            }
          });

          renderWithProviders(
            ValueHistoryText.getHistoryText({
              className,
              status: statusText,
              history,
              username,
              universalTracker: currencyUtr,
              unitConfig: emptyUnitConfig,
            }),
            { store }
          );

          expect(screen.getByTestId('action-username')).toBeInTheDocument();
          expect(screen.getByText('Submitted')).toBeInTheDocument();
          expect(screen.getByText('Full Name')).toBeInTheDocument();

          // Verify GBP currency is rendered correctly even with empty unitConfig
          expect(screen.getByTestId('history-text')).toBeInTheDocument();
          expect(screen.getByText('GBP')).toBeInTheDocument();
          expect(screen.getByText('1,000')).toBeInTheDocument();
        });

        it('should render non-currency universal trackers with unitConfig in DOM', () => {
          const history = generateValueHistory({
            action: UtrvStatus.Updated,
            value: 100,
            valueData: {
              input: { value: 100, unit: 'mJ' }
            }
          });

          renderWithProviders(
            ValueHistoryText.getHistoryText({
              className,
              status: statusText,
              history,
              username,
              universalTracker: numericUtr, // Non-currency tracker
              unitConfig: defaultUnitConfig,
            }),
            { store }
          );

          // Should still render correctly even for non-currency trackers
          expect(screen.getByTestId('action-username')).toBeInTheDocument();
          expect(screen.getByText('Submitted')).toBeInTheDocument();
          expect(screen.getByText('Full Name')).toBeInTheDocument();

          // Verify non-currency value is rendered correctly with unitConfig
          expect(screen.getByTestId('history-text')).toBeInTheDocument();
          expect(screen.getByText('100')).toBeInTheDocument();
          expect(screen.getByText('Megajoules')).toBeInTheDocument();
        });

        it('should render percentage tracker with unitConfig in DOM', () => {
          const history = generateValueHistory({
            action: UtrvStatus.Updated,
            value: 50,
            valueData: {
              input: { value: 50 }
            }
          });

          renderWithProviders(
            ValueHistoryText.getHistoryText({
              className,
              status: statusText,
              history,
              username,
              universalTracker: percentageUtr,
              unitConfig: defaultUnitConfig,
            }),
            { store }
          );

          expect(screen.getByTestId('action-username')).toBeInTheDocument();
          expect(screen.getByText('Submitted')).toBeInTheDocument();
          expect(screen.getByText('Full Name')).toBeInTheDocument();

          // Verify percentage value is rendered correctly with unitConfig
          expect(screen.getByTestId('history-text')).toBeInTheDocument();
          expect(screen.getByText('50')).toBeInTheDocument();
          expect(screen.getByText('%')).toBeInTheDocument();
        });

        it('should render Rejected status correctly in DOM (non-collapsible path)', () => {
          const history = generateValueHistory({
            action: UtrvStatus.Rejected,
            value: 1000,
            valueData: {
              input: { value: 1000, unit: 'GBP' }
            }
          });

          renderWithProviders(
            ValueHistoryText.getHistoryText({
              className,
              status: 'Rejected',
              history,
              username,
              universalTracker: currencyUtr,
              unitConfig: defaultUnitConfig,
            }),
            { store }
          );

          // Rejected status returns simple div, should render basic status
          expect(screen.getByTestId('action-username')).toBeInTheDocument();
          expect(screen.getByText(/Rejected/)).toBeInTheDocument();
          expect(screen.getByText(/Full Name/)).toBeInTheDocument();
        });

        it('should render Assured status correctly in DOM (non-collapsible path)', () => {
          const history = generateValueHistory({
            action: UtrvStatus.Verified,
            value: 1000,
            valueData: {
              input: { value: 1000, unit: 'GBP' }
            }
          });

          renderWithProviders(
            ValueHistoryText.getHistoryText({
              className,
              status: 'Assured',
              history,
              username,
              universalTracker: currencyUtr,
              unitConfig: defaultUnitConfig,
            }),
            { store }
          );

          // Assured status returns simple div, should render basic status
          expect(screen.getByTestId('action-username')).toBeInTheDocument();
          expect(screen.getByText(/Assured/)).toBeInTheDocument();
          expect(screen.getByText(/Full Name/)).toBeInTheDocument();
        });
      });
    });

    describe('Value List Multi', () => {
      const statusText = 'Updated';
      it('should render value list items', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          valueData: {
            data: ['option1', 'option2'],
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: valueListUtr,
          }),
        );
        const historyText = screen.getByTestId('history-text');
        expect(historyText.querySelectorAll('li')).toHaveLength(2);
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });
    });

    describe('Imported Data', () => {
      const statusText = 'Updated';
      it('should show imported data indicator', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          value: 10,
          valueData: {
            input: { value: 10 },
            isImported: true,
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );
        expect(screen.getByText('(via import file)')).toBeInTheDocument();
      });
    });

    describe('Rejected Status', () => {
      const statusText = 'Rejected';
      it('should render rejected status', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Rejected,
          value: 10,
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Rejected by Full Name');
      });
    });

    describe('Created Status', () => {
      const statusText = 'Created';
      it('should render created status', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Created,
          value: 10,
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Created by Full Name');
      });
    });

    
  });
});
