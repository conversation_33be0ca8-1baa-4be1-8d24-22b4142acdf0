/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */


import { valueListTestTable } from '../value-list-factory';
import { createCombinedFromCode, createUtr, createUtrv } from './utrv-factory';
import { TableColumnType, UtrValueType } from '../../types/universalTracker';
import { TableColumn } from '@components/survey/form/input/table/InputInterface';
import { ValueData } from '@components/survey/question/questionInterfaces';
import { UnitTypes } from '@utils/units';

const [first, second] = valueListTestTable.options;

export const tableOneColumnCodes = {
  col1: 'number_col1',
  col2: 'number_col2',
  col3: 'valueList_col3',
  col4: 'text_col4',
  col5: 'valueListMulti_col5',
  col6: 'text_with_listId_col6',
};

const tableValueValidation = {
  table: {
    columns: [
      {
        code: tableOneColumnCodes.col1,
        name: 'Number Col 1',
        shortName: 'Number Col 1',
        type: TableColumnType.Number,
      },
      {
        code: tableOneColumnCodes.col2,
        name: 'Number Col 2',
        shortName: 'Number Col 2',
        type: TableColumnType.Number,
      },
      {
        code: tableOneColumnCodes.col3,
        name: 'ValueList Col 3',
        shortName: 'ValueList Col 3',
        type: TableColumnType.ValueList,
        listId: valueListTestTable._id,
      },
      {
        code: tableOneColumnCodes.col4,
        name: 'Text Col 4',
        shortName: 'Text Col 4',
        type: TableColumnType.Text,
      },
      {
        code: tableOneColumnCodes.col5,
        name: 'valueListMulti_col5 Col 5',
        shortName: 'valueListMulti_col5 Col 5',
        type: TableColumnType.ValueListMulti,
        listId: valueListTestTable._id,
      },
      {
        code: tableOneColumnCodes.col6,
        name: 'text_listId_col6 Col 6',
        shortName: 'valueListMulti_col5 Col 5',
        type: TableColumnType.Text,
        listId: valueListTestTable._id,
      },
    ],
  },
};

export const utrTableOne = createUtr('table/one', { valueType: UtrValueType.Table, valueValidation: tableValueValidation});
export const utrSingleRowTableOne = createUtr('table/one', {
  valueType: UtrValueType.Table,
  valueValidation: { table: { ...tableValueValidation.table, validation: { maxRows: 1 } } },
});

const tableValueValidationWithCurrency = {
  table: {
    columns: [
      {
        code: 'currency_col',
        name: 'Currency Col 1',
        shortName: 'Currency Col 1',
        type: TableColumnType.Number,
        unitType: UnitTypes.currency,
        unit: 'USD',
      },
      {
        code: tableOneColumnCodes.col2,
        name: 'Number Col 2',
        shortName: 'Number Col 2',
        type: TableColumnType.Number,
      },
    ],
  },
};

export const utrSingleRowTableOneWithCurrency = createUtr('table/currency', {
  valueType: UtrValueType.Table,
  valueValidation: { table: { ...tableValueValidationWithCurrency.table, validation: { maxRows: 1 } } },
});

export const utrTableOneUtrv = createUtrv(utrTableOne._id, {
  valueData: {
    table: [
      [
        { code: tableOneColumnCodes.col1, value: 101, },
        { code: tableOneColumnCodes.col2, value: 102, },
        { code: tableOneColumnCodes.col3, value: first.code, },
        { code: tableOneColumnCodes.col4, value: 'text_col4_[0]_value', },
        { code: tableOneColumnCodes.col5, value: [first.code] },
        { code: tableOneColumnCodes.col6, value: first.code },
      ],
      [
        { code: tableOneColumnCodes.col1, value: 201, },
        { code: tableOneColumnCodes.col2, value: 202, },
        { code: tableOneColumnCodes.col3, value: second.code, },
        { code: tableOneColumnCodes.col4, value: 'text_col4_[1]_value', },
        { code: tableOneColumnCodes.col5, value: [first.code,second.code] },
        { code: tableOneColumnCodes.col6, value: second.code },
      ],
      [
        { code: tableOneColumnCodes.col1, value: 301, },
        { code: tableOneColumnCodes.col2, value: 302, },
        { code: tableOneColumnCodes.col3, value: second.code, },
        { code: tableOneColumnCodes.col4, value: 'text_col4_[0]_value', },
        { code: tableOneColumnCodes.col5, value: [first.code,second.code] },
        { code: tableOneColumnCodes.col6, value: first.code },
      ]
    ]
  }
})



const conversionColumns: TableColumn[] = [
  {
    code: 'number_col1_currency',
    name: 'Number Col With currency 1',
    shortName: '',
    type: TableColumnType.Number,
    unit: 'USD',
    unitType: 'currency',
    numberScale: 'millions',
  },
  {
    code: 'number_energy_col2',
    name: 'Number energy Col 2',
    shortName: '',
    type: TableColumnType.Number,
    unitType: 'energy',
    unit: 'mJ' //  1 mJ = 1000 kJ
  },
  {
    code: 'number_area_col3',
    name: 'Number area Col 3',
    shortName: '',
    type: TableColumnType.Number,
    unitType: 'area',
    unit: 'm2',
  },
];

// have currency and unit conversion
export const utrTableConversions = createUtr( 'table/conversions', {
  valueType: UtrValueType.Table,
  valueValidation: { table: { columns: conversionColumns } }
});

const [currencyCol, energyCol, areaCol] = conversionColumns;
// 1 mJ = 1000 kJ
// 1 m2 = 10000 ha Hectare
const valueData: ValueData = {
  table: [
    [
      { code: currencyCol.code, value: 101, },
      { code: energyCol.code, value: 102, },
      { code: areaCol.code, value: 103, },
    ],
    [
      { code: currencyCol.code, value: 201, },
      { code: energyCol.code, value: 202, },
      { code: areaCol.code, value: 203, },
    ]
  ],
  input: {
    table: [
      [
        { code: currencyCol.code, value: 0.101, numberScale: 'billions', unit: 'GBP' },
        { code: energyCol.code, value: 102000, unit: 'kJ' },
        { code: areaCol.code, value: 103_00000, unit: 'ha' },
      ],
      [
        { code: currencyCol.code, value: 0.201, numberScale: 'billions', unit: 'GBP' },
        { code: energyCol.code, value: 202_000, unit: 'kJ' },
        { code: areaCol.code, value: 203_00000, unit: 'ha' },
      ]
    ]
  },
};
export const utrTableConversionsUtrv = createUtrv(utrTableConversions._id, {
  valueData: valueData
});

export const utrvTavbleConversionsOne = createCombinedFromCode(
  utrTableConversions.code,
  utrTableConversionsUtrv,
  utrTableConversions,
);




export const utrGri_401_1_b = createUtr('gri/2020/401-1/b', {
  valueType: UtrValueType.Table,
  valueValidation: {
    table: {
      columns: [
        {
          'code': 'employee_hires_region',
          'name': 'Region',
          type: TableColumnType.Text,
          'instructions': 'Please select a region from the list or type your own custom option.',
          'shortName': 'Region',
          'validation': {
            'required': false,
            'allowCustomOptions': true
          },
          'listId': '5e81d4705922c919f758da12',
        },
        {
          'code': 'gender',
          'name': 'Gender',
          type: TableColumnType.Text,
          'instructions': 'Please select an option from the list.',
          'shortName': 'Gender',
          'listId': '5dfa37400788172ca96fba82',
        },
        {
          'code': 'under_30',
          'name': 'Total Employee Turnover Under 30 Years Old',
          type: TableColumnType.Number,
          'shortName': 'Total Number Under 30 Years Old',
        },
        {
          'code': 'age_30_50',
          'name': 'Total Employee Turnover 30-50 Years Old',
          type: TableColumnType.Number,
          'shortName': 'Total Number 30-50 Years Old',
        },
        {
          'code': 'over_50',
          'name': 'Total Employee Turnover Over 50 Years Old',
          type: TableColumnType.Number,
          'shortName': 'Total Number Over 50 Years Old',
        },
        {
          'code': 'under_30_rate',
          'name': 'Rate of Employee Turnover Under 30 Years',
          type: TableColumnType.Number,
          'calculation': {
            'formula': '({under_30}/({under_30}+{age_30_50}+{over_50}))*100'
          },
          'shortName': 'Rate Under 30 Years Old',
        },
        {
          'code': 'age_30_50_rate',
          'name': 'Rate of Employee Turnover 30-50 Years Old',
          type: TableColumnType.Number,
          'calculation': {
            'formula': '({age_30_50}/({under_30}+{age_30_50}+{over_50}))*100'
          },
          'shortName': 'Rate 30-50 Years Old',
        },
        {
          'code': 'over_50_rate',
          'name': 'Rate of Employee Turnover Over 50 Years Old',
          type: TableColumnType.Number,
          'calculation': {
            'formula': '({over_50}/({under_30}+{age_30_50}+{over_50}))*101'
          },
          'shortName': 'Rate Over 50 Years Old',
        },
        {
          'code': 'total_turnover',
          'name': 'Total Turnover Number',
          type: TableColumnType.Number,
          'calculation': {
            'formula': '{under_30}+{age_30_50}+{over_50}'
          },
          'shortName': 'Total Turnover',
        }
      ]
    },
  }
});

export const gri401_1_b = createUtrv(utrGri_401_1_b._id, {
  valueData: {
    table: [
      [
        { 'code': 'employee_hires_region', 'value': 'europe' },
        { 'code': 'gender', 'value': 'male1' },
        { 'code': 'under_30', 'value': '700' },
        { 'code': 'age_30_50', 'value': '200' },
        { 'code': 'over_50', 'value': '100' },
        { 'code': 'under_30_rate', 'value': '70' },
        { 'code': 'age_30_50_rate', 'value': '20' },
        { 'code': 'over_50_rate', 'value': '10.100000000000001' },
        { 'code': 'total_turnover', 'value': '1000' }
      ],
      [
        { 'code': 'employee_hires_region', 'value': 'europe' },
        { 'code': 'gender', 'value': 'male1' },
        { 'code': 'under_30', 'value': '300' },
        { 'code': 'age_30_50', 'value': '200' },
        { 'code': 'over_50', 'value': '300' },
        { 'code': 'under_30_rate', 'value': '37.5' },
        { 'code': 'age_30_50_rate', 'value': '25' },
        { 'code': 'over_50_rate', 'value': '37.875' },
        { 'code': 'total_turnover', 'value': '800' }
      ],
      [
        { 'code': 'employee_hires_region', 'value': 'europe' },
        { 'code': 'gender', 'value': 'females1' },
        { 'code': 'under_30', 'value': '100' },
        { 'code': 'age_30_50', 'value': '200' },
        { 'code': 'over_50', 'value': '300' },
        { 'code': 'under_30_rate', 'value': '16.666666666666664' },
        { 'code': 'age_30_50_rate', 'value': '33.33333333333333' },
        { 'code': 'over_50_rate', 'value': '50.5' },
        { 'code': 'total_turnover', 'value': '600' }
      ]
    ]
  }
})

export const utrGri_405_1_b = createUtr('gri/2020/405-1/b', {
  valueType: UtrValueType.Table,
  valueValidation: {
    table: {
      columns: [
        {
          'code': 'employee_category',
          'name': 'Employee Category',
          shortName: '',
          type: TableColumnType.Text,
          'instructions': 'Please select an option or type your own custom category, such as "All Employees".',
          'validation': {
            'required': false,
            'allowCustomOptions': true
          },
          'listId': '5e8748f061ff743189a881db',
        },
        {
          'code': 'employees_per_category',
          'name': 'Number of Employees per Selected Employee Category',
          shortName: '',
          type: TableColumnType.Number,
          'validation': {
            'required': false,
            'allowCustomOptions': true
          },
          'options': []
        },
        {
          'code': 'male',
          'name': 'i. a) Male',
          shortName: '',
          type: TableColumnType.Number,
          'validation': {
            'required': false,
            'allowCustomOptions': false,
            'min': 0,
            'max': 100
          },
        },
        {
          'code': 'female',
          'name': 'i. b) Female',
          shortName: '',
          type: TableColumnType.Number,
          'validation': {
            'required': false,
            'allowCustomOptions': false,
            'min': 0,
            'max': 100
          },
        },
        {
          'code': 'under_30',
          'name': 'ii. a) Under 30 Years Old',
          shortName: '',
          type: TableColumnType.Number,
          'validation': {
            'required': false,
            'allowCustomOptions': false,
            'min': 0,
            'max': 100
          },
        },
        {
          'code': 'age_30_50',
          'name': 'ii. b) 30-50 Years Old',
          shortName: '',
          type: TableColumnType.Number,
          'validation': {
            'required': false,
            'allowCustomOptions': false,
            'min': 0,
            'max': 100
          },
        },
        {
          'code': 'over_50',
          'name': 'ii. c) Over 50 Years Old',
          shortName: '',
          type: TableColumnType.Number,
          'validation': {
            'required': false,
            'allowCustomOptions': false,
            'min': 0,
            'max': 100
          },
        },
        {
          'code': 'disability',
          'name': 'iii. a) Disability Group',
          shortName: '',
          type: TableColumnType.Number,
          'validation': {
            'required': false,
            'allowCustomOptions': false,
            'min': 0,
            'max': 100
          },
        },
        {
          'code': 'minority',
          'name': 'iii. b) Minority Group',
          shortName: '',
          type: TableColumnType.Number,
          'validation': {
            'required': false,
            'allowCustomOptions': false,
            'min': 0,
            'max': 100
          },
        }
      ]
    }
  }
});

export const gri4051b = createUtrv(utrGri_405_1_b._id, {
  valueData: {
    table: [
      [
        { 'code': 'employee_category', 'value': 'senior_management' },
        { 'code': 'employees_per_category', 'value': '33' },
        { 'code': 'male', 'value': '48' },
        { 'code': 'female', 'value': '45' },
        { 'code': 'total_gender', 'value': '93' },
        { 'code': 'under_30', 'value': '32' },
        { 'code': 'age_30_50', 'value': '34' },
        { 'code': 'over_50', 'value': '34' },
        { 'code': 'total_by_age', 'value': '100' },
        { 'code': 'disability', 'value': '5' },
        { 'code': 'minority', 'value': '3' },
      ],
      [
        { 'code': 'employee_category', 'value': 'middle_management' },
        { 'code': 'employees_per_category', 'value': '5000' },
        { 'code': 'male', 'value': '72' },
        { 'code': 'female', 'value': '28' },
        { 'code': 'total_gender', 'value': '100' },
        { 'code': 'under_30', 'value': '73' },
        { 'code': 'age_30_50', 'value': '21' },
        { 'code': 'over_50', 'value': '6' },
        { 'code': 'total_by_age', 'value': '100' },
        { 'code': 'disability', 'value': '3' },
        { 'code': 'minority', 'value': '2' },
      ]
    ]
  }
})
